{"name": "@types/d3-dispatch", "version": "3.0.7", "description": "TypeScript definitions for d3-dispatch", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/d3-dispatch", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/tomwanzek"}, {"name": "<PERSON>", "githubUsername": "gust<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/gustavderdrache"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "denis<PERSON>", "githubUsername": "denis<PERSON>", "url": "https://github.com/denisname"}, {"name": "<PERSON>", "githubUsername": "Methuselah96", "url": "https://github.com/Methuselah96"}, {"name": "<PERSON>", "githubUsername": "k-yle", "url": "https://github.com/k-yle"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/d3-dispatch"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "be4052c6f2bf18bd5e9b8e514a0c6e7d09cd01d684460cef8a794c71e73e3627", "typeScriptVersion": "5.1"}