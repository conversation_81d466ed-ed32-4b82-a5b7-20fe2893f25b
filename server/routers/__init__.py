from fastapi import APIRouter
from . import chat, feedback, knowledge_graph, source, schema, parse, kg_build, kg_visualization

# 创建总路由器
api_router = APIRouter()

# 包含各个子路由器
api_router.include_router(chat.router, tags=["聊天"])
api_router.include_router(feedback.router, tags=["反馈"])
api_router.include_router(source.router, tags=["源内容"])
api_router.include_router(schema.router, prefix="/api", tags=["模板管理"])
api_router.include_router(parse.router, prefix="/api", tags=["文档解析"])
api_router.include_router(kg_build.router, prefix="/api", tags=["知识图谱构建"])
api_router.include_router(knowledge_graph.router, prefix="/api", tags=["知识图谱"])
# api_router.include_router(kg_visualization.router, prefix="/api", tags=["知识图谱可视化"])