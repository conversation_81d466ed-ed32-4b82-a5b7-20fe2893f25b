{"version": 3, "file": "semi.js", "sourceRoot": "", "sources": ["../../src/rules/semi.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAM1D,kCAAqC;AACrC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,MAAM,CAAC,CAAC;AAK3C,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,CAAC,oBAAoB,CAAC;QAClC,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,+CAA+C;YAC5D,oCAAoC;YACpC,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;QAC5B,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE;QACd,QAAQ;QACR;YACE,sBAAsB,EAAE,KAAK;YAC7B,gCAAgC,EAAE,KAAK;SACxC;KACF;IACD,MAAM,CAAC,OAAO;QACZ,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,iBAAiB,GACrB,KAAK,CAAC,mBAA2D,CAAC;QAEpE;;;;;;;UAOE;QACF,MAAM,YAAY,GAAG;YACnB,sBAAc,CAAC,kBAAkB;YACjC,sBAAc,CAAC,4BAA4B;YAC3C,sBAAc,CAAC,iBAAiB;YAChC,sBAAc,CAAC,kBAAkB;YACjC,sBAAc,CAAC,yBAAyB;YACxC,sBAAc,CAAC,sBAAsB;YACrC,sBAAc,CAAC,6BAA6B;SAC7C,CAAC,MAAM,CAAwB,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YAC5C,GAAG,CAAC,IAAc,CAAC,GAAG,iBAAiB,CAAC;YACxC,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;QAEP,OAAO;YACL,GAAG,KAAK;YACR,GAAG,YAAY;YACf,wBAAwB,CAAC,IAAI;gBAC3B,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB,EAAE,CAAC;oBACpE,KAAK,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;gBACvC,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}